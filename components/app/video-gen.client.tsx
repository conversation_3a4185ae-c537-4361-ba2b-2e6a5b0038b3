"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import {
	SparklesIcon,
	ImagePlus,
	X,
	CoinsIcon,
	CheckIcon,
	PaletteIcon,
	FileImageIcon,
	LoaderIcon,
	Download,
	Clock12Icon,
	ArrowUpRightIcon,
	ChevronLeftIcon,
} from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { downloadImageFromUrl, fileToBase64 } from "@/lib/file/utils-file";
import { calculateProgress, cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useMobile } from "@/hooks/use-mobile";
import { uploadFile } from "@/lib/file/upload-file";
import { Badge } from "../ui/badge";
import { IMAGE_SIZE_LIMIT_, OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { useSearchParams } from "next/navigation";
import { Icons } from "../icon/icons";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TO_VIDEO, EVENT_GEN_TEXT_TO_VIDEO } from "@/lib/track-events";
import { decodeBase64Url } from "@/lib/utils-url";
import { Hint } from "../ui/custom/hint";
import {
	getAspectRatioClass,
	getAspectRatioIcon,
	getInitialVideoModel,
	getVideoModelCredits,
	VideoModel,
	videoWithSeriesModels,
} from "@/config/video-models-config";
import { MediaResultStatus } from "@/@types/media/media-type";
import { PrefetchLink } from "../ui/custom/prefetch-link";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { Progress } from "../ui/custom/progress";
import { WarningBanner } from "../shared/banner-nsfw";

const changeAspectRatio = (aspectRatioAll: string[] | undefined, currentAspectRatio: string) => {
	if (!aspectRatioAll) return "16:9";
	if (aspectRatioAll.includes(currentAspectRatio)) {
		return currentAspectRatio;
	}
	return aspectRatioAll[0];
};
const changeDurationExist = (durationAll: number[] | undefined, duration: number) => {
	if (!durationAll) return 3;
	if (durationAll.includes(duration)) {
		return duration;
	}
	return durationAll[0];
};

const formatTakenTime = (seconds: number) => {
	if (seconds < 100) {
		return `${Math.floor(seconds)} sec`;
	}
	return `${Math.floor(seconds / 60)} min`;
};

// Function to parse base64 and extract prompt and model
const parseBase64Params = (base64Str: string) => {
	try {
		const payload = JSON.parse(decodeBase64Url(base64Str));

		return {
			prompt: (payload.prompt as string) || "",
			model: (payload.model as string) || "",
		};
	} catch (error) {
		console.error("Failed to decode base64:", error);
		return { prompt: "", model: "" };
	}
};

export default function VideoClient() {
	const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid, isLoaded: userIsLoaded } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const isMobile = useMobile();

	const modelParam = searchParams.get("model");
	const initialModel = getInitialVideoModel(modelParam);

	const [visibility, setVisibility] = useState<string>("public");
	const [model, setModel] = useState<VideoModel>(initialModel);
	const [modelStyle, setModelStyle] = useState<string | null>(initialModel.modelStyle ? initialModel.modelStyle[0].id : null);
	const [prompt, setPrompt] = useState("");
	const [aspectRatio, setAspectRatio] = useState<string>(initialModel.aspectRatioAll?.[0] || "16:9");
	const [resolution, setResolution] = useState<string | null>(initialModel.resolutionAll?.[0] || null);
	const [duration, setDuration] = useState<number>(initialModel.durationAll?.[0] || 3);

	const [paramsInfo, setParamsInfo] = useState<{
		model: VideoModel;
		aspectRatioClass: string;
	}>({
		model: initialModel,
		aspectRatioClass: getAspectRatioClass(aspectRatio),
	});
	const [startImage, setStartImage] = useState<{
		url: string;
		base64: string;
	} | null>(null);

	// Mobile model selector states
	const [showMobileModelSelector, setShowMobileModelSelector] = useState(false);
	const [mobileModelView, setMobileModelView] = useState<"series" | "models">("series");
	const [selectedSeries, setSelectedSeries] = useState<(typeof videoWithSeriesModels)[0] | null>(null);

	useEffect(() => {
		const paramsBase64Str = searchParams.get("params");
		if (paramsBase64Str) {
			const { prompt: paramPrompt, model: paramModel } = parseBase64Params(paramsBase64Str);

			// Set prompt if available
			if (paramPrompt) {
				setPrompt(paramPrompt);
			}

			// Set model if available and valid
			if (paramModel) {
				// Find the model in the available models
				const foundModel = videoWithSeriesModels.flatMap((series) => series.models).find((modelOption) => modelOption.id === paramModel);

				if (foundModel) {
					setModel(foundModel);
					// Set model style if the model has styles
					if (foundModel.modelStyle) {
						setModelStyle(foundModel.modelStyle[0].id);
					}
				}
			}
		}
	}, []);

	const [uploadingStartImage, setUploadingStartImage] = useState<boolean>(false);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}
			let file = acceptedFiles[0];
			if (!file) return;

			if (file.size > IMAGE_SIZE_LIMIT_) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingStartImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setStartImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingStartImage(false);
			}
		},
	});

	const [previewVideo, setPreviewVideo] = useState<string | null>(null);
	const [submitting, setSubmitting] = useState<boolean>(false);
	const { seconds } = useSubmitTimer(submitting);

	// Mobile model selector handlers
	const handleMobileModelSelect = (selectedModel: VideoModel) => {
		if (!userHasPaid && selectedModel.pro) {
			setPlanBoxOpen(true);
			return;
		}
		setModel(selectedModel);
		setAspectRatio(changeAspectRatio(selectedModel.aspectRatioAll, aspectRatio));
		setDuration(changeDurationExist(selectedModel.durationAll, duration));
		if (selectedModel.resolutionAll) {
			setResolution(selectedModel.resolutionAll[0]);
		}
		if (selectedModel.modelStyle) {
			setModelStyle(selectedModel.modelStyle[0].id);
		}
		setShowMobileModelSelector(false);
		setMobileModelView("series");
		setSelectedSeries(null);
	};

	const handleMobileSeriesSelect = (series: (typeof videoWithSeriesModels)[0]) => {
		setSelectedSeries(series);
		setMobileModelView("models");
	};

	const handleMobileBackToSeries = () => {
		setMobileModelView("series");
		setSelectedSeries(null);
	};

	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		setParamsInfo({
			model: model,
			aspectRatioClass: getAspectRatioClass(aspectRatio),
		});

		if (!model.textToVideo && !startImage) {
			toast.warning("This model should have an image input.");
			return;
		}

		sendGTMEvent({
			event: startImage?.url ? EVENT_GEN_IMAGE_TO_VIDEO : EVENT_GEN_TEXT_TO_VIDEO,
			membership_level: user?.membershipLevel,
			model: model.id,
		});

		try {
			setPreviewVideo(null);
			setSubmitting(true);

			// request task
			const { status, message, task_status, request_id } = await ofetch("/api/v1/video/generate-video", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					model: model.id,
					prompt: promtpTrim,
					aspectRatio: aspectRatio,
					duration: duration,
					image: startImage?.url,
					resolution: resolution,
					modelStyle: modelStyle,
					visibility: visibility,
				},
			});
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;
			await new Promise((resolve) => setTimeout(resolve, (paramsInfo.model.time ?? 60) * 1000));

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 7500)); // wait for 7.5 seconds
				let {
					status: request_status,
					message: request_message,
					taskStatus: reuqest_taskStatus,
					taskError: request_taskError,
					resultUrls,
				} = await ofetch("/api/v1/video/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = reuqest_taskStatus;
				taskError = request_taskError;
				if (resultUrls?.[0]) {
					setPreviewVideo(resultUrls[0]);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Generate video failed. Please try again or contact support.");
			}

			// setPreviewVideo(
			// 	`${OSS_URL_HOST}mkt/pages/ai-video-generator/text-to-video.mp4`
			// );
		} catch (error: any) {
			setPreviewVideo(null);
			console.error("Failed to generate video:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="bg-muted w-full rounded-xl px-4 py-2 backdrop-blur-3xl md:rounded-3xl">
				{startImage && model.startFrame && (
					<div className="group relative h-14 w-24">
						<img
							src={startImage.base64}
							alt="Model"
							className="h-full w-full rounded-md object-cover"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
						/>
						<button
							className="absolute -top-1.5 -right-1.5 z-10 rounded-full"
							onClick={(e) => {
								e.stopPropagation();
								setStartImage(null);
							}}
						>
							<X className="size-[18px] rounded-full bg-zinc-900 p-1 text-white" strokeWidth={3} />
						</button>
					</div>
				)}
				<Textarea
					placeholder="Describe your video scene..."
					value={prompt}
					maxLength={1500}
					onChange={(e) => setPrompt(e.target.value)}
					className="min-h-[88px] resize-none border-none px-0 shadow-none placeholder:text-base focus-visible:text-base focus-visible:ring-0 md:text-base [&::-webkit-scrollbar]:hidden"
				/>

				<div className="mt-2 mb-1 flex items-center justify-between gap-1">
					<div className="flex flex-wrap items-center gap-1">
						<Hint label={`${visibility === "public" ? "Public: Anyone can see and use" : "Private: Only you can see and use"}`}>
							<div
								className={cn(
									buttonVariants({ variant: "secondary", size: "sm" }),
									"hover:ring-input h-8 cursor-pointer gap-0.5 rounded-full bg-white py-0 text-xs shadow-none hover:bg-white hover:ring-2",
								)}
								onClick={() => {
									if (visibility === "public" && !userHasPaid) {
										setPlanBoxOpen(true);
										return;
									}
									if (visibility === "public") {
										setVisibility("private");
									} else {
										setVisibility("public");
									}
								}}
							>
								{visibility === "public" ? "Public" : "Private"}
								{!userHasPaid && <Icons.Lock className="size-3.5" />}
							</div>
						</Hint>

						{/* Model Selector - Desktop */}
						{!isMobile && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between gap-0.5 rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<img className="size-[14px]" src={model.logo} alt="" />
										{model?.name}
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-auto rounded-xl">
									{videoWithSeriesModels.map((series, index) => (
										<DropdownMenuSub key={index}>
											<DropdownMenuSubTrigger
												className={cn(
													"my-1 cursor-pointer items-center gap-3 rounded-lg p-3",
													model.name.includes(series.name) && "bg-muted",
												)}
											>
												<div className="flex flex-col gap-1">
													<div className="flex items-center gap-2">
														<img className="size-4" src={series.logo} alt="" />
														<p className="font-semibold">{series.name}</p>
														{series.new && <Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>}
													</div>
													<p className="text-muted-foreground">{series.description}</p>
												</div>
											</DropdownMenuSubTrigger>
											<DropdownMenuSubContent className="w-auto min-w-[180px] space-y-1 rounded-xl">
												{series.models.map((modelOption, index) => (
													<DropdownMenuItem
														key={index}
														className={cn(
															"w-full cursor-pointer items-center rounded-lg p-3",
															model.id === modelOption.id && "bg-muted",
															!userHasPaid && modelOption.pro && "opacity-60",
														)}
														onClick={() => {
															if (!userHasPaid && modelOption.pro) {
																setPlanBoxOpen(true);
																return;
															}
															setModel(modelOption);
															setAspectRatio(changeAspectRatio(modelOption.aspectRatioAll, aspectRatio));
															setDuration(changeDurationExist(modelOption.durationAll, duration));
															if (modelOption.resolutionAll) {
																setResolution(modelOption.resolutionAll[0]);
															}
															if (modelOption.modelStyle) {
																setModelStyle(modelOption.modelStyle[0].id);
															}
														}}
													>
														<div className="flex w-full flex-row items-center justify-between gap-3">
															<div className="flex flex-col gap-0.5">
																<p className="font-medium">
																	{modelOption.name}
																	{modelOption.new && (
																		<Badge className="bg-action ml-1 rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																	)}
																</p>
																<p className="text-muted-foreground text-xs">{modelOption.description}</p>
																<div className="flex flex-row flex-wrap items-center gap-1">
																	{modelOption.time && (
																		<Badge
																			variant="secondary"
																			className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																		>
																			<Clock12Icon className="size-[10px]" /> {formatTakenTime(modelOption.time)}
																		</Badge>
																	)}
																	{modelOption.startFrame && (
																		<Badge
																			variant="secondary"
																			className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																		>
																			<FileImageIcon className="size-2.5" />
																			Start frame
																		</Badge>
																	)}
																	{/* {modelOption.endFrame && (
																	<Badge variant="secondary" className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]">
																		<FileImageIcon className="size-2.5" />
																		End frame
																	</Badge>
																)} */}
																	{modelOption.modelStyle && (
																		<Badge
																			variant="secondary"
																			className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																		>
																			<PaletteIcon className="size-2.5" />
																			Model style
																		</Badge>
																	)}
																</div>
															</div>
															{model.id === modelOption.id && (
																<p className="flex h-4 w-4 shrink-0 items-center justify-center rounded-full bg-black">
																	<CheckIcon className="size-3 text-white" strokeWidth={3} />
																</p>
															)}
														</div>
													</DropdownMenuItem>
												))}
											</DropdownMenuSubContent>
										</DropdownMenuSub>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						)}

						{/* Model Selector - Mobile */}
						{isMobile && (
							<>
								<Button
									variant="secondary"
									size="sm"
									className="hover:ring-input justify-between gap-0.5 rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									onClick={() => setShowMobileModelSelector(true)}
								>
									<img className="size-[14px]" src={model.logo} alt="" />
									{model?.name}
								</Button>

								<Sheet open={showMobileModelSelector} onOpenChange={setShowMobileModelSelector}>
									<SheetContent side="bottom" className="h-[80vh] gap-0 rounded-t-xl">
										<SheetHeader className={cn(mobileModelView === "series" ? "mb-4 h-0 p-0" : "")}>
											<SheetTitle className="flex items-center gap-2">
												{mobileModelView === "models" && (
													<Button
														variant="ghost"
														size="sm"
														onClick={handleMobileBackToSeries}
														className="text-muted-foreground h-auto p-1 font-semibold"
													>
														<ChevronLeftIcon className="size-4" />
														Go back
													</Button>
												)}
											</SheetTitle>
										</SheetHeader>

										<div className="overflow-y-auto px-4 pb-4">
											{mobileModelView === "series" && (
												<>
													{videoWithSeriesModels.map((series, index) => (
														<div
															key={index}
															className={cn(
																"hover:bg-muted cursor-pointer rounded-lg p-4",
																model.name.includes(series.name) && "bg-muted",
															)}
															onClick={() => handleMobileSeriesSelect(series)}
														>
															<div className="flex items-center gap-3">
																<img className="size-6" src={series.logo} alt="" />
																<div className="flex-1">
																	<div className="flex items-center gap-2">
																		<p className="font-semibold">{series.name}</p>
																		{series.new && (
																			<Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																		)}
																	</div>
																	<p className="text-muted-foreground text-sm">{series.description}</p>
																</div>
															</div>
														</div>
													))}
												</>
											)}

											{mobileModelView === "models" && selectedSeries && (
												<>
													{selectedSeries.models.map((modelOption, index) => (
														<div
															key={index}
															className={cn(
																"cursor-pointer rounded-lg p-4",
																model.id === modelOption.id ? "bg-muted" : "hover:bg-muted",
																!userHasPaid && modelOption.pro && "opacity-60",
															)}
															onClick={() => handleMobileModelSelect(modelOption)}
														>
															<div className="flex items-center justify-between gap-3">
																<div className="flex-1">
																	<div className="flex items-center gap-2">
																		<p className="font-medium">{modelOption.name}</p>
																		{modelOption.new && (
																			<Badge className="bg-action rounded-sm px-1.5 py-[1px] text-[10px]">New</Badge>
																		)}
																	</div>
																	<p className="text-muted-foreground text-sm">{modelOption.description}</p>
																	<div className="mt-1 flex flex-row flex-wrap items-center gap-1">
																		{modelOption.time && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<Clock12Icon className="size-[10px]" /> {formatTakenTime(modelOption.time)}
																			</Badge>
																		)}
																		{modelOption.startFrame && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<FileImageIcon className="size-2.5" />
																				Start frame
																			</Badge>
																		)}
																		{modelOption.modelStyle && (
																			<Badge
																				variant="secondary"
																				className="bg-input/60 rounded-sm px-1.5 py-[1px] text-[10px]"
																			>
																				<PaletteIcon className="size-2.5" />
																				Model style
																			</Badge>
																		)}
																	</div>
																</div>
																{model.id === modelOption.id && (
																	<p className="flex h-4 w-4 shrink-0 items-center justify-center rounded-full bg-black">
																		<CheckIcon className="size-3 text-white" strokeWidth={3} />
																	</p>
																)}
															</div>
														</div>
													))}
												</>
											)}
										</div>
									</SheetContent>
								</Sheet>
							</>
						)}

						{model.startFrame && (
							<div>
								<div
									{...getRootProps()}
									className={cn(
										buttonVariants({ variant: "secondary", size: "sm" }),
										"hover:ring-input h-8 cursor-pointer gap-0.5 rounded-full bg-white py-0 text-xs shadow-none hover:bg-white hover:ring-2",
										(startImage || uploadingStartImage) && "cursor-not-allowed opacity-60",
									)}
									onClick={() => {
										if (startImage || uploadingStartImage) return;
										openDropzone();
									}}
								>
									{uploadingStartImage ? (
										<LoaderIcon className="text-foreground size-3.5 animate-spin" />
									) : (
										<ImagePlus className="size-3.5" />
									)}
									Start frame
									<input {...getInputProps()} />
								</div>
							</div>
						)}

						{model.noAspectRatio !== "all" && !(model.noAspectRatio === "image-to-video" && startImage) && model.aspectRatioAll && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<div className="flex items-center gap-0.5">
											{/* <imageSize.icon className="size-3.5" /> */}
											{aspectRatio}
										</div>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[160px]">
									<p className="text-muted-foreground px-2 py-1.5 text-sm">Aspect ratio</p>
									{model.aspectRatioAll.map((aspectRatioOption, index) => {
										const IconComponent = getAspectRatioIcon(aspectRatioOption);
										return (
											<DropdownMenuItem
												key={index}
												className={cn(aspectRatio === aspectRatioOption && "bg-muted", "group w-full cursor-pointer justify-between")}
												onClick={() => setAspectRatio(aspectRatioOption)}
											>
												<div className="flex items-center gap-2">
													<IconComponent className="size-4" />
													{aspectRatioOption}
												</div>
												{aspectRatio === aspectRatioOption && (
													<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
														<CheckIcon className="size-3 text-white" strokeWidth={3} />
													</p>
												)}
											</DropdownMenuItem>
										);
									})}
								</DropdownMenuContent>
							</DropdownMenu>
						)}

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="secondary"
									size="sm"
									className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
								>
									<div className="flex items-center gap-0.5">{duration}s</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-[160px]">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Duration</p>
								{model.durationAll.map((durationOption, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(duration === durationOption && "bg-muted", "group w-full cursor-pointer justify-between")}
										onClick={() => setDuration(durationOption)}
									>
										<div className="flex items-center gap-2">{durationOption}s</div>
										{duration === durationOption && (
											<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>

						{model.resolutionAll && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<div className="flex items-center gap-0.5">{resolution}</div>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[160px]">
									<p className="text-muted-foreground px-2 py-1.5 text-sm">Resolution</p>
									{model.resolutionAll.map((resolutionOption, index) => (
										<DropdownMenuItem
											key={index}
											className={cn(resolution === resolutionOption && "bg-muted", "group w-full cursor-pointer justify-between")}
											onClick={() => setResolution(resolutionOption)}
										>
											<div className="flex items-center gap-2">{resolutionOption}</div>
											{resolution === resolutionOption && (
												<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
													<CheckIcon className="size-3 text-white" strokeWidth={3} />
												</p>
											)}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						)}

						{model.modelStyle && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="secondary"
										size="sm"
										className="hover:ring-input justify-between rounded-full bg-white text-xs font-medium shadow-none hover:bg-white hover:ring-2"
									>
										<div className="flex items-center gap-0.5">
											<PaletteIcon className="size-3.5" />
											{model.modelStyle.find((modelStyleOption) => modelStyleOption.id === modelStyle)?.name}
										</div>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[140px]">
									<p className="text-muted-foreground px-2 py-1.5 text-sm">Model Style</p>
									{model.modelStyle.map((modelStyleOption, index) => (
										<DropdownMenuItem
											key={index}
											className={cn("w-full cursor-pointer justify-between", modelStyle === modelStyleOption.id && "bg-muted")}
											onClick={() => setModelStyle(modelStyleOption.id)}
										>
											<div className="flex items-center gap-2">{modelStyleOption.name}</div>
											{modelStyle === modelStyleOption.id && (
												<p className="flex h-4 w-4 items-center justify-center rounded-full bg-black">
													<CheckIcon className="size-3 text-white" strokeWidth={3} />
												</p>
											)}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						)}
					</div>

					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0}
						className="bg-foreground hover:bg-foreground h-10"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								{getVideoModelCredits(model.credits, model.creditsMethod, duration, resolution)})
							</p>
						)}
					</SubmitButton>
				</div>
			</div>
			<div className="-mt-4 flex w-full">
				<PrefetchLink href="/video-tools/video-prompt-generator" className="bg-muted hover:bg-input flex rounded-lg px-2 py-1">
					<p className="flex flex-row items-center gap-0.5 text-[11px]">
						{/* <WandSparklesIcon className="size-2.5" /> */}
						Video Prompt Generator <ArrowUpRightIcon className="size-3" />
					</p>
				</PrefetchLink>
			</div>

			{/* NSFW Warning */}
			<WarningBanner localStorageKey="nsfw-warning-dismissed-video" />

			<div className="w-full">
				{submitting ? (
					<div className="bg-muted mx-auto flex aspect-video w-full max-w-2xl shrink-0 rounded-lg">
						<div className="text-secondary-foreground mx-auto flex flex-col items-center justify-center gap-2 px-4 text-center">
							<span className="text-sm tabular-nums">{seconds}s</span>
							<div className="flex flex-row items-center gap-1 text-xs">
								<Progress
									value={calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}
									// value={50}
									className="bg-input h-1.5 w-[280px]"
									indicatorClassName="bg-action"
								/>
								<span className="font-mono tabular-nums">{calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}%</span>
							</div>
							<div className="text-xs">
								{WEBNAME} is generating your video, which may take {paramsInfo.model.time} seconds. Once finished, the video will be saved in{" "}
								<NoPrefetchLink href="/assets" target="_blank" className="text-action hover:underline hover:underline-offset-4">
									My Assets
								</NoPrefetchLink>
								.
							</div>
						</div>
					</div>
				) : (
					previewVideo && (
						<div className={cn("group relative mx-auto w-full max-w-2xl rounded-lg")}>
							<video
								src={previewVideo}
								muted
								controls
								controlsList="nodownload noplaybackrate"
								disableRemotePlayback
								className="h-full w-full rounded-xl object-contain"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
								onMouseEnter={(e) => (e.target as HTMLVideoElement).play()}
							/>
							<div className="absolute top-2 right-2 z-10 items-center gap-1">
								{previewVideo && (
									<Hint label="Download">
										<div className="relative">
											<SubmitButton
												className="bg-foreground hover:bg-foreground/80"
												isSubmitting={downloading}
												disabled={!previewVideo}
												size="icon"
												onClick={async () => {
													try {
														setDownloading(true);
														await downloadImageFromUrl(previewVideo);
													} catch (error) {
														console.error("Failed to download:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					)
				)}
			</div>
		</div>
	);
}
