import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { getMediaItemsRealtime, getMediaTask } from "@/server/utils-media.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaItem, MediaTask } from "@/server/db/schema.server";
import { handleApiError } from "@/@types/error-api";

interface Params {
	id: string;
}

/**
 * 获取图片生成状态
 */
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.id) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("userId: ", sessionUser.id);
	}

	try {
		const mediaTask: MediaTask | null = await getMediaTask(params.id, sessionUser.id);
		if (!mediaTask) {
			return NextResponse.json({ status: 404, message: "Generate task not found." });
		}
		if (mediaTask.status === MediaResultStatus.Completed) {
			const mediaItems: MediaItem[] | null = await getMediaItemsRealtime(mediaTask.mediaHeadUid!, sessionUser.id);
			if (!mediaItems) {
				return NextResponse.json({ status: 404, message: "Task reuslt not found." });
			}
			return NextResponse.json({
				status: 200,
				taskStatus: mediaTask.status,
				resultUrls: mediaItems.map((item) => {
					return `${OSS_URL_HOST}${item.mediaPaths}`;
				}),
			});
		}
		return NextResponse.json({ status: 200, taskStatus: mediaTask.status });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/status`);
	}
}
