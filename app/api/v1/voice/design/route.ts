import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { WEBNAME, CALLBACK_URL_WAVESPEED } from "@/lib/constants";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserModelCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_DESIGN_VOICE } from "@/lib/track-events";
import { voiceModelTaskSchema } from "@/server/db/schema.server";
import { getUUIDString } from "@/lib/utils";
import { ofetch } from "ofetch";

type Params = {
	prompt: string;
	textPreview: string;
	name: string;
	visibility: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.prompt || !params.textPreview || !params.name) {
		return NextResponse.json({ status: 400, message: "Required parameters are missing (prompt, textPreview, name)." });
	}

	try {
		const userId = await getSessionUserId();

		const { creditConsumes, membershipLevel } = await checkUserModelCredit(userId, {
			needCredits: 1,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_DESIGN_VOICE, userId, {
			mp_country_code: cfIpCountryCode,
			membershipLevel: membershipLevel,
		});

		const customVoiceId = `voicedesi-${getUUIDString()}`;

		// Call Wavespeed API
		const wavespeedPayload = {
			prompt: params.prompt,
			text: params.textPreview,
			custom_voice_id: customVoiceId,
		};

		if (process.env.NODE_ENV === "development") {
			console.log("Wavespeed API payload: ", wavespeedPayload);
			console.log("Webhook URL: ", CALLBACK_URL_WAVESPEED);
		}

		const wavespeedResponse = await ofetch(`https://api.wavespeed.ai/api/v3/minimax/voice-design?webhook=${encodeURIComponent(CALLBACK_URL_WAVESPEED)}`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
			body: wavespeedPayload,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("Wavespeed API response: ", wavespeedResponse);
		}

		const wavespeedRequestId = wavespeedResponse.data.id;

		// save to db
		const db = getDB();
		await db.insert(voiceModelTaskSchema).values({
			userId: userId,
			thirdRequestId: wavespeedRequestId, // Use Wavespeed's request ID
			customVoiceId: customVoiceId,
			status: MediaResultStatus.InProgress,
			visibility: params.visibility === "public",
			aiModelId: 1, // Default AI model ID, you may want to make this configurable
			prompt: params.prompt,
			textPreview: params.textPreview,
			name: params.name,
			description: params.prompt || null,
			creditsSources: JSON.stringify(creditConsumes),
			ip: cfIp,
		});

		// 更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Voice design task request id: ${wavespeedRequestId}.`,
		});

		return NextResponse.json({
			status: 200,
			message: "Success",
			task_status: MediaResultStatus.InProgress,
			request_id: wavespeedRequestId,
		});
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/voice/design`);
	}
}
